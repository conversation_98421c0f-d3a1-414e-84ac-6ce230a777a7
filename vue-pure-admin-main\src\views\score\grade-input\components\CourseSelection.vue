<template>
  <div class="main-content">
    <PureTableBar :title="`${majorName} - ${className} - 课程选择`" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-space>
          <el-select
            v-model="form.semesterId"
            placeholder="请选择学期"
            clearable
            @change="onSearch"
            style="width: 180px"
          >
            <el-option
              v-for="semester in semesters"
              :key="semester.id"
              :label="semester.semesterName"
              :value="semester.id"
            />
          </el-select>
          <el-input
            v-model="form.courseName"
            placeholder="课程名称"
            clearable
            @input="onSearch"
            style="width: 150px"
          />
          <el-select
            v-model="form.courseType"
            placeholder="课程类型"
            clearable
            @change="onSearch"
            style="width: 120px"
          >
            <el-option label="必修" value="必修" />
            <el-option label="选修" value="选修" />
            <el-option label="实践" value="实践" />
          </el-select>
          <el-button type="primary" @click="onSearch">
            搜索
          </el-button>
          <el-button @click="onReset">
            重置
          </el-button>
          <el-button type="info" @click="handleBack">
            返回
          </el-button>
        </el-space>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="courseData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { getClassCoursePage } from "@/api/educational/classCourse";
import { getAllSemesters } from "@/api/basic/semester";
import { PureTableBar } from "@/components/RePureTableBar";

import type { PaginationProps } from "@pureadmin/table";
import type { TableColumnList } from "@pureadmin/table";

defineOptions({
  name: "CourseSelection"
});

// Props
interface Props {
  classCode: string;
  className: string;
  majorName: string;
}

const props = withDefaults(defineProps<Props>(), {
  classCode: "",
  className: "",
  majorName: ""
});

// Emits
const emit = defineEmits<{
  selectCourse: [course: { courseCode: string; courseName: string }];
  back: [];
}>();

// 响应式数据
const loading = ref(true);
const tableRef = ref();

// 搜索表单
const form = reactive({
  semesterId: null as number | null,
  courseName: "",
  courseType: ""
});

// 学期列表
const semesters = ref([]);

// 课程数据
const courseData = ref([]);

// 分页配置
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

// 表格列配置
const columns: TableColumnList = [
  {
    label: "序号",
    type: "index",
    width: 70,
    align: "center",
    index: (index) => {
      return (pagination.currentPage - 1) * pagination.pageSize + index + 1;
    }
  },
  {
    label: "课程代码",
    prop: "courseCode",
    width: 120,
    align: "center"
  },
  {
    label: "课程名称",
    prop: "courseName",
    minWidth: 150,
    align: "center"
  },
  {
    label: "课程类型",
    prop: "courseType",
    width: 100,
    align: "center"
  },
  {
    label: "学分",
    prop: "credits",
    width: 80,
    align: "center"
  },
  {
    label: "学期",
    prop: "semesterName",
    width: 120,
    align: "center"
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    align: "center",
    cellRenderer: ({ row }) => {
      return h("el-button", {
        type: "primary",
        size: "small",
        onClick: () => handleSelectCourse(row)
      }, "选择录入");
    }
  }
];

// 获取学期列表
const getSemesters = async () => {
  try {
    const { data } = await getAllSemesters();
    semesters.value = data || [];
  } catch (error) {
    console.error("获取学期列表失败:", error);
    message("获取学期列表失败", { type: "error" });
  }
};

// 获取课程数据
const getCourses = async () => {
  if (!props.classCode) {
    return;
  }

  loading.value = true;
  try {
    const params = {
      current: pagination.currentPage,
      size: pagination.pageSize,
      classCode: props.classCode,
      semesterId: form.semesterId,
      courseName: form.courseName,
      courseType: form.courseType
    };

    const { data } = await getClassCoursePage(params);

    if (data) {
      courseData.value = data.records || [];
      pagination.total = data.total || 0;
    }
  } catch (error) {
    console.error("获取课程数据失败:", error);
    message("获取课程数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  getCourses();
};

// 重置
const onReset = () => {
  form.semesterId = null;
  form.courseName = "";
  form.courseType = "";
  pagination.currentPage = 1;
  getCourses();
};

// 返回
const handleBack = () => {
  emit('back');
};

// 选择课程
const handleSelectCourse = (course: any) => {
  emit('selectCourse', {
    courseCode: course.courseCode,
    courseName: course.courseName,
    semesterId: course.semesterId,
    semesterName: course.semesterName
  });
};

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  getCourses();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getCourses();
};

// 组件挂载
onMounted(() => {
  getSemesters();
  getCourses();
});
</script>

