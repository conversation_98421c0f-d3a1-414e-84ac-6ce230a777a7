<template>
  <div class="main-content">
    <PureTableBar
      :title="`${majorName} - ${className} - ${courseName} - ${semesterName} - 成绩录入`"
      :columns="columns"
      @refresh="onSearch"
    >
      <template #buttons>
        <el-space>
          <el-input
            v-model="form.studentName"
            placeholder="学生姓名"
            clearable
            @input="onSearch"
            style="width: 150px"
          />
          <el-input
            v-model="form.studentId"
            placeholder="学号"
            clearable
            @input="onSearch"
            style="width: 150px"
          />
          <el-button type="primary" @click="onSearch">
            搜索
          </el-button>
          <el-button @click="onReset">
            重置
          </el-button>
          <el-button type="info" @click="handleBack">
            返回
          </el-button>
          <el-button type="success" @click="handleBatchInput">
            批量录入
          </el-button>
          <el-button type="warning" @click="handleImport">
            导入成绩
          </el-button>
        </el-space>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="gradeData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 成绩录入弹窗 -->
    <GradeInputDialog
      v-model="inputDialogVisible"
      :grade-data="currentGrade"
      :course-code="courseCode"
      :course-name="courseName"
      :semester-id="semesterId"
      @success="handleInputSuccess"
    />

    <!-- 批量录入弹窗 -->
    <BatchInputDialog
      v-model="batchDialogVisible"
      :class-code="classCode"
      :class-name="className"
      :course-code="courseCode"
      :course-name="courseName"
      :semester-id="semesterId"
      @success="handleBatchSuccess"
    />

    <!-- 导入成绩弹窗 -->
    <ImportDialog
      v-model="importDialogVisible"
      :class-code="classCode"
      :class-name="className"
      :course-code="courseCode"
      :course-name="courseName"
      :semester-id="semesterId"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed, watch, h } from "vue";
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
import { getStudentGradeInputList, deleteGrade } from "@/api/score/grade-input";
import { PureTableBar } from "@/components/RePureTableBar";
import GradeInputDialog from "./GradeInputDialog.vue";
import BatchInputDialog from "./BatchInputDialog.vue";
import ImportDialog from "./ImportDialog.vue";

import type { TableColumnList } from "@pureadmin/table";
import type { GradeInputVO } from "@/api/score/grade-input";

defineOptions({
  name: "GradeInputList"
});

// Props
interface Props {
  classCode: string;
  className: string;
  majorName: string;
  courseCode: string;
  courseName: string;
  semesterId: number;
  semesterName: string;
}

const props = withDefaults(defineProps<Props>(), {
  classCode: "",
  className: "",
  majorName: "",
  courseCode: "",
  courseName: "",
  semesterId: 0,
  semesterName: ""
});

// Emits
const emit = defineEmits<{
  back: [];
}>();

// 响应式数据
const loading = ref(true);
const tableRef = ref();

// 搜索表单
const form = reactive({
  studentName: "",
  studentId: ""
});

// 成绩数据
const gradeData = ref<GradeInputVO[]>([]);

// 弹窗控制
const inputDialogVisible = ref(false);
const batchDialogVisible = ref(false);
const importDialogVisible = ref(false);
const currentGrade = ref<GradeInputVO | null>(null);

// 表格列配置
const columns: TableColumnList = [
  {
    label: "序号",
    type: "index",
    width: 70,
    align: "center"
  },
  {
    label: "学号",
    prop: "studentId",
    width: 120,
    align: "center"
  },
  {
    label: "姓名",
    prop: "studentName",
    width: 100,
    align: "center"
  },
  {
    label: "期末成绩",
    prop: "finalScore",
    width: 100,
    align: "center",
    cellRenderer: ({ row }) => {
      if (row.hasGrade && row.finalScore !== null && row.finalScore !== undefined) {
        return row.finalScore.toFixed(2);
      }
      return "-";
    }
  },
  {
    label: "绩点",
    prop: "gradePoint",
    width: 80,
    align: "center",
    cellRenderer: ({ row }) => {
      if (row.hasGrade && row.gradePoint !== null && row.gradePoint !== undefined) {
        return row.gradePoint.toFixed(2);
      }
      return "-";
    }
  },
  {
    label: "是否重修",
    prop: "isRetake",
    width: 100,
    align: "center",
    cellRenderer: ({ row }) => {
      if (row.hasGrade) {
        return row.isRetake ? "是" : "否";
      }
      return "-";
    }
  },
  {
    label: "录入状态",
    prop: "hasGrade",
    width: 100,
    align: "center",
    cellRenderer: ({ row }) => {
      return h("el-tag", {
        type: row.hasGrade ? "success" : "info"
      }, row.hasGrade ? "已录入" : "未录入");
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 160,
    align: "center",
    cellRenderer: ({ row }) => {
      const buttons = [
        h("el-button", {
          type: "primary",
          size: "small",
          onClick: () => handleEdit(row)
        }, row.hasGrade ? "编辑" : "录入")
      ];

      if (row.hasGrade) {
        buttons.push(
          h("el-button", {
            type: "danger",
            size: "small",
            onClick: () => handleDelete(row)
          }, "删除")
        );
      }

      return h("el-space", {}, buttons);
    }
  }
];

// 不再需要获取学期列表，直接使用传入的学期信息

// 获取成绩数据
const getGrades = async () => {
  if (!props.classCode || !props.courseCode || !props.semesterId) {
    return;
  }

  loading.value = true;
  try {
    const { data } = await getStudentGradeInputList(props.classCode, props.courseCode, props.semesterId);
    gradeData.value = data || [];
  } catch (error) {
    console.error("获取成绩数据失败:", error);
    message("获取成绩数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  getGrades();
};

// 重置
const onReset = () => {
  form.studentName = "";
  form.studentId = "";
  getGrades();
};

// 返回
const handleBack = () => {
  emit('back');
};

// 编辑/录入成绩
const handleEdit = (row: GradeInputVO) => {
  currentGrade.value = row;
  inputDialogVisible.value = true;
};

// 删除成绩
const handleDelete = async (row: GradeInputVO) => {
  if (!row.id) return;

  try {
    await ElMessageBox.confirm("确定要删除这条成绩记录吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    await deleteGrade(row.id);
    message("删除成功", { type: "success" });
    getGrades();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除成绩失败:", error);
      message("删除失败", { type: "error" });
    }
  }
};

// 批量录入
const handleBatchInput = () => {
  batchDialogVisible.value = true;
};

// 录入成功回调
const handleInputSuccess = () => {
  message("操作成功", { type: "success" });
  getGrades();
};

// 批量录入成功回调
const handleBatchSuccess = () => {
  message("批量录入成功", { type: "success" });
  getGrades();
};

// 导入成绩
const handleImport = () => {
  importDialogVisible.value = true;
};

// 导入成功回调
const handleImportSuccess = () => {
  message("导入成功", { type: "success" });
  getGrades();
};

// 组件挂载
onMounted(() => {
  getGrades();
});
</script>

